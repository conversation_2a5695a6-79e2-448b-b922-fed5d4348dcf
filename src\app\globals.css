@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* 导航栏动画类 */
.animate-fadeIn {
  animation: fadeInDown 0.2s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 导航链接悬停效果 */
.nav-link-hover:hover::after {
  width: 100%;
}

.nav-link-hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #6366f1, #a855f7);
  transition: width 0.3s ease;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.8);
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 支付弹窗滚动区域优化 */
.payment-scroll-area {
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
}

/* 移动设备触摸优化 */
@media (max-width: 768px) {
  .payment-scroll-area {
    /* 移动端滚动优化 */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
  html, body {
    overscroll-behavior: none; /* 防止iOS橡皮筋效果 */
    -webkit-overflow-scrolling: touch; /* 保持滚动惯性 */
    overflow-x: hidden; /* 防止水平滚动 */
  }

  * {
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }

  .overflow-scroll, .overflow-auto, .overflow-y-auto, .overflow-y-scroll {
    -webkit-overflow-scrolling: touch; /* 平滑滚动 */
  }

  /* 提高按钮和可点击元素的触摸面积 */
  button, [role="button"], a {
    min-height: 44px;
    min-width: 44px;
  }

  /* 修复固定定位底栏在iOS中的显示问题 */
  .sticky.bottom-0 {
    position: -webkit-sticky;
    position: sticky;
    transform: translateZ(0); /* 开启硬件加速 */
    z-index: 10;
  }

  /* 确保模态框显示正常 */
  [role="dialog"] {
    touch-action: none; /* 禁止模态框触摸事件 */
  }

  /* 优化滚动性能 */
  main {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }

  /* 确保ProductCard不会导致滚动中断 */
  [class*="ProductCard"] {
    transform: translateZ(0);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}
