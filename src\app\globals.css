@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* 导航栏动画类 */
.animate-fadeIn {
  animation: fadeInDown 0.2s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 导航链接悬停效果 */
.nav-link-hover:hover::after {
  width: 100%;
}

.nav-link-hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #6366f1, #a855f7);
  transition: width 0.3s ease;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.8);
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 支付弹窗滚动区域优化 */
.payment-scroll-area {
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
}

/* 移动设备触摸优化 */
@media (max-width: 768px) {
  .payment-scroll-area {
    /* 移动端滚动优化 */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
  html, body {
    overscroll-behavior: none; /* 防止iOS橡皮筋效果 */
    -webkit-overflow-scrolling: touch; /* 保持滚动惯性 */
    overflow-x: hidden; /* 防止水平滚动 */
  }

  * {
    -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
  }

  .overflow-scroll, .overflow-auto, .overflow-y-auto, .overflow-y-scroll {
    -webkit-overflow-scrolling: touch; /* 平滑滚动 */
  }

  /* 提高按钮和可点击元素的触摸面积 */
  button, [role="button"], a {
    min-height: 44px;
    min-width: 44px;
  }

  /* 修复固定定位底栏在iOS中的显示问题 */
  .sticky.bottom-0 {
    position: -webkit-sticky;
    position: sticky;
    transform: translateZ(0); /* 开启硬件加速 */
    z-index: 10;
  }

  /* 确保模态框显示正常 */
  [role="dialog"] {
    touch-action: none; /* 禁止模态框触摸事件 */
  }

  /* 优化滚动性能 */
  main {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }

  /* 确保ProductCard不会导致滚动中断 */
  [class*="ProductCard"] {
    transform: translateZ(0);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* 手机端登录按钮优化 */
  button[class*="gradient"] {
    /* 增强触摸反馈 */
    -webkit-tap-highlight-color: rgba(34, 197, 94, 0.3);
    tap-highlight-color: rgba(34, 197, 94, 0.3);

    /* 防止双击缩放 */
    touch-action: manipulation;

    /* 优化按钮按压效果 */
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 登录按钮特殊效果 */
  button[class*="from-green-600"] {
    /* 添加微妙的阴影动画 */
    box-shadow:
      0 4px 14px 0 rgba(34, 197, 94, 0.25),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  /* 按压时的阴影效果 */
  button[class*="from-green-600"]:active {
    box-shadow:
      0 2px 8px 0 rgba(34, 197, 94, 0.35),
      0 1px 2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 手机端按钮文字优化 */
  button span {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* 手机端登录区域特殊效果 */
  .mobile-login-area {
    /* 背景动画效果 */
    background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.1) 0%,
      rgba(20, 184, 166, 0.1) 50%,
      rgba(34, 197, 94, 0.05) 100%);

    /* 边框发光效果 */
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow:
      0 0 20px rgba(34, 197, 94, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* 动画效果 */
    animation: mobile-login-glow 3s ease-in-out infinite alternate;
  }

  @keyframes mobile-login-glow {
    0% {
      box-shadow:
        0 0 20px rgba(34, 197, 94, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow:
        0 0 30px rgba(34, 197, 94, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }

  /* 手机端登录按钮特殊动画 */
  .mobile-login-button {
    position: relative;
    overflow: hidden;
  }

  .mobile-login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    transition: left 0.5s;
  }

  .mobile-login-button:hover::before {
    left: 100%;
  }

  /* 手机端用户信息区域特殊效果 */
  .mobile-user-area {
    /* 背景动画效果 */
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 51, 234, 0.1) 50%,
      rgba(59, 130, 246, 0.05) 100%);

    /* 边框发光效果 */
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    /* 动画效果 */
    animation: mobile-user-glow 4s ease-in-out infinite alternate;
  }

  @keyframes mobile-user-glow {
    0% {
      box-shadow:
        0 0 20px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow:
        0 0 30px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }

  /* 手机端退出登录按钮特殊动画 */
  .mobile-logout-button {
    position: relative;
    overflow: hidden;
  }

  .mobile-logout-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    transition: left 0.5s;
  }

  .mobile-logout-button:hover::before {
    left: 100%;
  }

  /* 退出登录按钮特殊效果 */
  button[class*="from-red-600"] {
    /* 添加微妙的阴影动画 */
    box-shadow:
      0 4px 14px 0 rgba(220, 38, 38, 0.25),
      0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  /* 按压时的阴影效果 */
  button[class*="from-red-600"]:active {
    box-shadow:
      0 2px 8px 0 rgba(220, 38, 38, 0.35),
      0 1px 2px 0 rgba(0, 0, 0, 0.15);
  }

  /* 在线状态指示器动画 */
  @keyframes online-pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  .online-indicator {
    animation: online-pulse 2s ease-in-out infinite;
  }
}
